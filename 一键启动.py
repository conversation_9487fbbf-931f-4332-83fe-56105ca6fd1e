#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动脚本 - 运行所有数据获取脚本
将数据保存为CSV格式到指定目录
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('一键启动.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
CSV_OUTPUT_DIR = "K:\\数据库存储地"
SCRIPTS = [
    {
        'name': '全量股票日线数据',
        'file': '全量股票日线.py',
        'description': '获取所有股票的日线数据'
    },
    {
        'name': '指数全量数据',
        'file': '指数全量.py',
        'description': '获取主要指数的历史数据'
    },
    {
        'name': '财务数据',
        'file': '财务数据分开表格全量更新.py',
        'description': '获取所有股票的财务数据（资产负债表、利润表、现金流量表）'
    },
    {
        'name': '分红数据',
        'file': '分红数据分开表格全量更新.py',
        'description': '获取所有股票的分红数据'
    }
]

def check_output_directory():
    """检查输出目录是否存在，不存在则创建"""
    try:
        # 创建主目录和子目录
        directories = [
            CSV_OUTPUT_DIR,
            os.path.join(CSV_OUTPUT_DIR, "股票日线数据"),
            os.path.join(CSV_OUTPUT_DIR, "指数数据"),
            os.path.join(CSV_OUTPUT_DIR, "财务数据"),
            os.path.join(CSV_OUTPUT_DIR, "财务数据", "资产负债表"),
            os.path.join(CSV_OUTPUT_DIR, "财务数据", "利润表"),
            os.path.join(CSV_OUTPUT_DIR, "财务数据", "现金流量表"),
            os.path.join(CSV_OUTPUT_DIR, "分红数据")
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info(f"输出目录结构已准备: {CSV_OUTPUT_DIR}")
        logger.info("子目录包括: 股票日线数据、指数数据、财务数据、分红数据")
        return True
    except Exception as e:
        logger.error(f"无法创建输出目录结构 {CSV_OUTPUT_DIR}: {e}")
        return False

def check_script_exists(script_file):
    """检查脚本文件是否存在"""
    return os.path.exists(script_file)

def run_script(script_info):
    """运行单个脚本"""
    script_file = script_info['file']
    script_name = script_info['name']
    description = script_info['description']
    
    logger.info(f"开始运行: {script_name}")
    logger.info(f"描述: {description}")
    logger.info(f"脚本文件: {script_file}")
    
    if not check_script_exists(script_file):
        logger.error(f"脚本文件不存在: {script_file}")
        return False
    
    try:
        start_time = time.time()
        
        # 运行脚本
        result = subprocess.run(
            [sys.executable, script_file],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            logger.info(f"✓ {script_name} 运行成功 (耗时: {duration:.1f}秒)")
            if result.stdout:
                logger.info(f"输出: {result.stdout}")
            return True
        else:
            logger.error(f"✗ {script_name} 运行失败 (返回码: {result.returncode})")
            if result.stderr:
                logger.error(f"错误信息: {result.stderr}")
            if result.stdout:
                logger.info(f"输出: {result.stdout}")
            return False
            
    except Exception as e:
        logger.error(f"运行脚本 {script_name} 时发生异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("="*60)
    logger.info("开始执行一键启动脚本")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("="*60)
    
    # 检查输出目录
    if not check_output_directory():
        logger.error("输出目录检查失败，程序退出")
        return False
    
    # 显示将要运行的脚本
    logger.info("将要运行的脚本:")
    for i, script in enumerate(SCRIPTS, 1):
        logger.info(f"{i}. {script['name']} - {script['description']}")
    
    # 询问用户是否继续
    try:
        user_input = input("\n是否继续执行所有脚本? (y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            logger.info("用户取消执行")
            return False
    except KeyboardInterrupt:
        logger.info("\n用户中断执行")
        return False
    
    # 运行所有脚本
    success_count = 0
    total_start_time = time.time()
    
    for i, script in enumerate(SCRIPTS, 1):
        logger.info(f"\n{'='*40}")
        logger.info(f"执行第 {i}/{len(SCRIPTS)} 个脚本")
        logger.info(f"{'='*40}")
        
        if run_script(script):
            success_count += 1
        else:
            # 询问是否继续执行下一个脚本
            try:
                user_input = input(f"\n脚本 {script['name']} 执行失败，是否继续执行下一个脚本? (y/n): ").strip().lower()
                if user_input not in ['y', 'yes', '是']:
                    logger.info("用户选择停止执行")
                    break
            except KeyboardInterrupt:
                logger.info("\n用户中断执行")
                break
    
    # 总结
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    logger.info("\n" + "="*60)
    logger.info("执行完成总结")
    logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
    logger.info(f"成功执行: {success_count}/{len(SCRIPTS)} 个脚本")
    logger.info(f"数据保存位置: {CSV_OUTPUT_DIR}")
    logger.info("文件夹结构:")
    logger.info("  ├── 股票日线数据/")
    logger.info("  ├── 指数数据/")
    logger.info("  ├── 财务数据/")
    logger.info("  │   ├── 资产负债表/")
    logger.info("  │   ├── 利润表/")
    logger.info("  │   └── 现金流量表/")
    logger.info("  └── 分红数据/")
    logger.info("="*60)
    
    if success_count == len(SCRIPTS):
        logger.info("🎉 所有脚本执行成功！")
        return True
    else:
        logger.warning(f"⚠️  有 {len(SCRIPTS) - success_count} 个脚本执行失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n执行过程中有错误，请检查日志。按回车键退出...")
    except KeyboardInterrupt:
        logger.info("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        input("\n按回车键退出...")
