# 股票数据获取工具 - 分类文件夹版本

## 🎉 修改完成

已成功将所有脚本修改为按类型分类存储CSV文件到不同文件夹中。

## 📁 新的文件夹结构

```
K:\数据库存储地\
├── 股票日线数据/           # 所有股票的日线数据
│   ├── stock_daily_000001.csv
│   ├── stock_daily_000002.csv
│   └── ...
├── 指数数据/               # 主要指数的历史数据
│   ├── index_000001_sh.csv
│   ├── index_399001_sz.csv
│   └── ...
├── 财务数据/               # 财务报表数据
│   ├── 资产负债表/
│   │   ├── balance_sheet_000001_sz.csv
│   │   └── ...
│   ├── 利润表/
│   │   ├── income_statement_000001_sz.csv
│   │   └── ...
│   └── 现金流量表/
│       ├── cash_flow_000001_sz.csv
│       └── ...
└── 分红数据/               # 分红派息数据
    ├── dividend_000001_sz.csv
    └── ...
```

## 🔄 主要修改内容

### 1. 全量股票日线.py
- ✅ 添加 `STOCK_DAILY_DIR` 变量
- ✅ 所有股票日线数据保存到 `股票日线数据/` 文件夹

### 2. 指数全量.py
- ✅ 添加 `INDEX_DIR` 变量
- ✅ 所有指数数据保存到 `指数数据/` 文件夹

### 3. 财务数据分开表格全量更新.py
- ✅ 添加 `FINANCIAL_DIR`、`BALANCE_SHEET_DIR`、`INCOME_STATEMENT_DIR`、`CASH_FLOW_DIR` 变量
- ✅ 资产负债表保存到 `财务数据/资产负债表/` 文件夹
- ✅ 利润表保存到 `财务数据/利润表/` 文件夹
- ✅ 现金流量表保存到 `财务数据/现金流量表/` 文件夹

### 4. 分红数据分开表格全量更新.py
- ✅ 添加 `DIVIDEND_DIR` 变量
- ✅ 所有分红数据保存到 `分红数据/` 文件夹

### 5. 一键启动.py
- ✅ 更新 `check_output_directory()` 函数，自动创建所有子文件夹
- ✅ 添加文件夹结构显示

### 6. 文档更新
- ✅ 更新 `README.md` 添加文件夹结构说明
- ✅ 更新 `使用说明.txt` 添加文件夹结构
- ✅ 更新 `启动数据获取.bat` 添加结构说明

## 🚀 使用方法

### 快速开始
1. 双击运行 `启动数据获取.bat`
2. 程序会自动创建所有必要的文件夹
3. 按照提示操作即可

### 手动运行
```bash
python 一键启动.py
```

## ✨ 新增功能

1. **自动文件夹创建**: 程序启动时自动创建完整的文件夹结构
2. **分类存储**: 不同类型的数据存储在对应的文件夹中，便于管理
3. **结构显示**: 启动时显示完整的文件夹结构
4. **测试工具**: 提供 `测试文件夹结构.py` 用于验证文件夹创建

## 📋 文件清单

### 主要脚本
- `全量股票日线.py` - 股票日线数据获取
- `指数全量.py` - 指数数据获取
- `财务数据分开表格全量更新.py` - 财务数据获取
- `分红数据分开表格全量更新.py` - 分红数据获取

### 控制脚本
- `一键启动.py` - 主控制脚本
- `启动数据获取.bat` - Windows批处理启动文件

### 测试工具
- `测试脚本.py` - 语法检查工具
- `测试文件夹结构.py` - 文件夹结构测试工具

### 文档
- `README.md` - 详细说明文档
- `使用说明.txt` - 简化使用说明
- `修改完成说明.md` - 本文档

## 🎯 优势

1. **组织清晰**: 数据按类型分类存储，便于查找和管理
2. **自动化**: 一键创建完整的文件夹结构
3. **易于维护**: 清晰的文件夹层次结构
4. **扩展性好**: 可以轻松添加新的数据类型文件夹

## 💡 注意事项

- 首次运行会自动创建所有必要的文件夹
- 确保有足够的磁盘空间存储数据
- 建议在非交易时间运行以获得更好的网络性能

## 🔧 故障排除

如果遇到文件夹创建问题：
1. 检查磁盘空间是否充足
2. 确认对目标目录有写入权限
3. 运行 `测试文件夹结构.py` 进行诊断

---

**修改完成时间**: 2024年
**版本**: 分类文件夹版本 v1.0
