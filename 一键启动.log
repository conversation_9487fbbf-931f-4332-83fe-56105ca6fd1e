2025-08-10 16:32:57,004 - INFO - ============================================================
2025-08-10 16:32:57,005 - INFO - 开始执行一键启动脚本
2025-08-10 16:32:57,005 - INFO - 开始时间: 2025-08-10 16:32:57
2025-08-10 16:32:57,006 - INFO - ============================================================
2025-08-10 16:32:57,007 - INFO - 输出目录已准备: K:\数据库存储地
2025-08-10 16:32:57,007 - INFO - 将要运行的脚本:
2025-08-10 16:32:57,007 - INFO - 1. 全量股票日线数据 - 获取所有股票的日线数据
2025-08-10 16:32:57,007 - INFO - 2. 指数全量数据 - 获取主要指数的历史数据
2025-08-10 16:32:57,008 - INFO - 3. 财务数据 - 获取所有股票的财务数据（资产负债表、利润表、现金流量表）
2025-08-10 16:32:57,008 - INFO - 4. 分红数据 - 获取所有股票的分红数据
2025-08-10 16:33:03,994 - INFO - 
========================================
2025-08-10 16:33:03,994 - INFO - 执行第 1/4 个脚本
2025-08-10 16:33:03,994 - INFO - ========================================
2025-08-10 16:33:03,995 - INFO - 开始运行: 全量股票日线数据
2025-08-10 16:33:03,995 - INFO - 描述: 获取所有股票的日线数据
2025-08-10 16:33:03,995 - INFO - 脚本文件: 全量股票日线.py
2025-08-10 16:33:51,532 - INFO - 
程序被用户中断
