import tushare as ts
import pandas as pd
import logging
from datetime import datetime
import time
import re
import numpy as np
import os
from typing import List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dividend_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
CSV_OUTPUT_DIR = "K:\\数据库存储地"
DIVIDEND_DIR = os.path.join(CSV_OUTPUT_DIR, "分红数据")

# Initialize
try:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    # Create output directories if they don't exist
    os.makedirs(CSV_OUTPUT_DIR, exist_ok=True)
    os.makedirs(DIVIDEND_DIR, exist_ok=True)
    logger.info("Tushare API initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Tushare API: {e}")
    raise



def get_all_stocks():
    """Get all stock codes"""
    try:
        logger.info("Fetching all stock codes...")
        stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        logger.info(f"Found {len(stock_basic)} stocks")
        return stock_basic['ts_code'].tolist()
    except Exception as e:
        logger.error(f"Error fetching stock codes: {e}")
        raise

def clean_filename(ts_code):
    """Convert stock code to valid filename"""
    return re.sub(r'[^\w]', '_', ts_code.lower())

def clean_dataframe(df):
    """Clean dataframe for MySQL compatibility"""
    if df.empty:
        return df

    # Replace NaN with None for MySQL compatibility
    df = df.replace({np.nan: None})

    # Convert date columns to proper format
    date_columns = ['end_date', 'ann_date', 'record_date', 'ex_date', 'pay_date', 'div_listdate', 'imp_ann_date', 'base_date']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')

    # Ensure string columns are properly encoded
    string_columns = ['div_proc']  # Add other string columns as needed
    for col in string_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).replace('nan', None)

    return df

def fetch_all_dividend_data():
    """Fetch dividend data for all stocks - save as CSV files"""

    # Get all stock codes
    try:
        stock_codes = get_all_stocks()
    except Exception as e:
        logger.error(f"Failed to get stock codes: {e}")
        return False

    success_count = 0
    error_count = 0

    for i, stock_code in enumerate(stock_codes):
        logger.info(f"Processing {stock_code} ({i+1}/{len(stock_codes)})")

        try:
            file_suffix = clean_filename(stock_code)

            # Dividend data
            dividend_df = pro.dividend(
                ts_code=stock_code,
                fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date,div_listdate,imp_ann_date,base_date,base_share'
            )

            if not dividend_df.empty:
                # Clean the dataframe
                dividend_df = clean_dataframe(dividend_df)

                # Sort by date (ascending order - oldest to newest)
                dividend_df = dividend_df.sort_values('end_date', ascending=True)

                # Save to CSV
                csv_filename = f'dividend_{file_suffix}.csv'
                csv_filepath = os.path.join(DIVIDEND_DIR, csv_filename)
                dividend_df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')

                logger.info(f"  Dividend: {len(dividend_df)} records saved")
                success_count += 1
            else:
                logger.info(f"  Dividend: No data for {stock_code}")

            # Rate limiting - avoid hitting API limits
            time.sleep(0.2)

        except Exception as e:
            logger.error(f"Error processing {stock_code}: {e}")
            error_count += 1
            continue

    logger.info(f"Processing completed. Success: {success_count}, Errors: {error_count}")
    return True

def fetch_combined_dividend_data():
    """Fetch dividend data for all stocks - single combined CSV file"""

    # Get all stock codes
    stock_codes = get_all_stocks()
    all_dividend_data = []

    for i, stock_code in enumerate(stock_codes):
        print(f"Processing {stock_code} ({i+1}/{len(stock_codes)})")

        try:
            # Dividend data
            dividend_df = pro.dividend(
                ts_code=stock_code,
                fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date,div_listdate,imp_ann_date,base_date,base_share'
            )

            if not dividend_df.empty:
                dividend_df = clean_dataframe(dividend_df)
                all_dividend_data.append(dividend_df)
                print(f"  Dividend: {len(dividend_df)} records")
            else:
                print(f"  Dividend: No data")

            # Rate limiting - avoid hitting API limits
            time.sleep(0.2)

        except Exception as e:
            print(f"Error processing {stock_code}: {e}")
            continue

    # Combine all dividend data
    if all_dividend_data:
        print("Combining and storing dividend data...")
        combined_dividend = pd.concat(all_dividend_data, ignore_index=True)

        # Sort by date (ascending order - oldest to newest)
        combined_dividend = combined_dividend.sort_values('end_date', ascending=True)

        # Save to CSV
        csv_filepath = os.path.join(CSV_OUTPUT_DIR, 'all_dividend_data.csv')
        combined_dividend.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
        print(f"All dividend data: {len(combined_dividend)} records stored")
    else:
        print("No dividend data found")

def main():
    """Main function with proper error handling"""
    logger.info("Starting dividend data fetch process")

    try:
        # Test basic functionality first
        logger.info("Testing API connection...")
        test_stocks = get_all_stocks()[:5]  # Test with first 5 stocks
        logger.info(f"API test successful. Found {len(test_stocks)} test stocks")

        # Choose one of the following:

        # Option 1: Separate table per stock
        success = fetch_all_dividend_data()

        # Option 2: Single combined table (uncomment to use)
        # success = fetch_combined_dividend_data()

        if success:
            logger.info("Process completed successfully")
        else:
            logger.error("Process completed with errors")

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":
    main()
