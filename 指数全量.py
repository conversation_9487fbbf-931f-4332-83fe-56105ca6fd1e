import tushare as ts
import pandas as pd
import logging
import os

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
CSV_OUTPUT_DIR = "K:\\数据库存储地"
INDEX_DIR = os.path.join(CSV_OUTPUT_DIR, "指数数据")

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# Create output directories if they don't exist
os.makedirs(CSV_OUTPUT_DIR, exist_ok=True)
os.makedirs(INDEX_DIR, exist_ok=True)

def fetch_and_store_index_data():
    """Fetch all index data and store as CSV files"""
    
    # List of all indices to fetch
    indices = [
        '000001.SH',  # sh000001 - Shanghai Composite Index
        '000016.SH',  # sh000016 - SSE 50 Index
        '000300.SH',  # sh000300 - CSI 300 Index
        '000852.SH',  # sh000852 - CSI 1000 Index
        '000905.SH',  # sh000905 - CSI 500 Index
        '932000.CSI', # CSI 2000 Index - 中证2000指数
        '399001.SZ',  # sz399001 - Shenzhen Component Index (替代932000.SH)
        '399006.SZ'   # sz399006 - ChiNext Index
    ]
    
    for ts_code in indices:
        try:
            print(f"Fetching data for {ts_code}...")
            
            # Fetch all available index data
            df = pro.index_daily(ts_code=ts_code)
            
            # Rename columns to match your requirements
            df = df.rename(columns={
                'trade_date': 'candle_end_time',
                'ts_code': 'index_code',
                'vol': 'volume'
            })
            
            # Select required columns
            df = df[['candle_end_time', 'open', 'high', 'low', 'close', 'amount', 'volume', 'index_code']]
            
            # Convert trade_date to datetime format
            df['candle_end_time'] = pd.to_datetime(df['candle_end_time'])

            # Sort by date (ascending order - oldest to newest)
            df = df.sort_values('candle_end_time', ascending=True)

            # Create CSV filename based on index code
            csv_filename = f'index_{ts_code.lower().replace(".", "_")}.csv'
            csv_filepath = os.path.join(INDEX_DIR, csv_filename)

            # Save to CSV
            df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')

            print(f"Successfully saved {len(df)} records for {ts_code} to {csv_filename}")
            
        except Exception as e:
            logging.error(f"Error fetching/storing data for {ts_code}: {e}")

if __name__ == "__main__":
    fetch_and_store_index_data()
