#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件夹结构创建
"""

import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_folder_structure():
    """创建完整的文件夹结构"""
    CSV_OUTPUT_DIR = "K:\\数据库存储地"
    
    # 定义所有需要创建的目录
    directories = [
        CSV_OUTPUT_DIR,
        os.path.join(CSV_OUTPUT_DIR, "股票日线数据"),
        os.path.join(CSV_OUTPUT_DIR, "指数数据"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "资产负债表"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "利润表"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "现金流量表"),
        os.path.join(CSV_OUTPUT_DIR, "分红数据")
    ]
    
    logger.info("开始创建文件夹结构...")
    
    try:
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"✓ 创建目录: {directory}")
        
        logger.info("\n文件夹结构创建完成！")
        logger.info("目录结构如下:")
        logger.info(f"{CSV_OUTPUT_DIR}/")
        logger.info("├── 股票日线数据/")
        logger.info("├── 指数数据/")
        logger.info("├── 财务数据/")
        logger.info("│   ├── 资产负债表/")
        logger.info("│   ├── 利润表/")
        logger.info("│   └── 现金流量表/")
        logger.info("└── 分红数据/")
        
        return True
        
    except Exception as e:
        logger.error(f"创建文件夹结构失败: {e}")
        return False

def check_folder_structure():
    """检查文件夹结构是否存在"""
    CSV_OUTPUT_DIR = "K:\\数据库存储地"
    
    directories = [
        CSV_OUTPUT_DIR,
        os.path.join(CSV_OUTPUT_DIR, "股票日线数据"),
        os.path.join(CSV_OUTPUT_DIR, "指数数据"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "资产负债表"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "利润表"),
        os.path.join(CSV_OUTPUT_DIR, "财务数据", "现金流量表"),
        os.path.join(CSV_OUTPUT_DIR, "分红数据")
    ]
    
    logger.info("检查文件夹结构...")
    
    all_exist = True
    for directory in directories:
        if os.path.exists(directory):
            logger.info(f"✓ 存在: {directory}")
        else:
            logger.warning(f"✗ 不存在: {directory}")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("文件夹结构测试工具")
    logger.info("=" * 60)
    
    # 检查现有结构
    if check_folder_structure():
        logger.info("\n🎉 所有文件夹都已存在！")
    else:
        logger.info("\n需要创建文件夹结构...")
        if create_folder_structure():
            logger.info("\n🎉 文件夹结构创建成功！")
        else:
            logger.error("\n❌ 文件夹结构创建失败！")
            return False
    
    logger.info("\n" + "=" * 60)
    logger.info("测试完成")
    logger.info("=" * 60)
    return True

if __name__ == "__main__":
    main()
    input("按回车键退出...")
