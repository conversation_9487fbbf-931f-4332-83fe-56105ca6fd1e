# 股票数据获取工具

## 概述
本工具集用于从Tushare获取股票相关数据，并将数据保存为CSV格式到指定目录。

## 功能特性
- ✅ 获取全量股票日线数据（包含复权因子）
- ✅ 获取主要指数历史数据
- ✅ 获取财务数据（资产负债表、利润表、现金流量表）
- ✅ 获取分红数据
- ✅ 数据按日期升序排列（从旧到新）
- ✅ 支持中文编码（UTF-8-BOM）
- ✅ 一键启动所有脚本

## 数据存储位置
所有CSV文件将保存到：`K:\数据库存储地`

## 使用方法

### 方法一：使用批处理文件（推荐）
1. 双击运行 `启动数据获取.bat`
2. 按照提示操作

### 方法二：使用Python脚本
1. 运行 `python 一键启动.py`
2. 按照提示操作

### 方法三：单独运行脚本
可以单独运行以下任一脚本：
- `全量股票日线.py` - 获取股票日线数据
- `指数全量.py` - 获取指数数据
- `财务数据分开表格全量更新.py` - 获取财务数据
- `分红数据分开表格全量更新.py` - 获取分红数据

## 输出文件说明

### 股票日线数据
- 文件名格式：`stock_daily_{股票代码}.csv`
- 包含字段：交易日期、开盘价、最高价、最低价、收盘价、成交量、成交额等
- 包含复权价格和复权因子

### 指数数据
- 文件名格式：`index_{指数代码}.csv`
- 包含主要指数：上证综指、深证成指、沪深300、中证500、中证1000等

### 财务数据
每个股票生成三个文件：
- `balance_sheet_{股票代码}.csv` - 资产负债表
- `income_statement_{股票代码}.csv` - 利润表
- `cash_flow_{股票代码}.csv` - 现金流量表

### 分红数据
- 文件名格式：`dividend_{股票代码}.csv`
- 包含分红派息相关信息

## 系统要求
- Python 3.6+
- 必需的Python包：
  - tushare
  - pandas
  - numpy

## 安装依赖
```bash
pip install tushare pandas numpy
```

## 注意事项
1. 需要有效的Tushare Token
2. 数据获取可能需要较长时间，请耐心等待
3. 建议在网络稳定的环境下运行
4. 如果某个脚本失败，可以选择继续执行下一个脚本
5. 所有日志信息会保存到对应的.log文件中

## 日志文件
- `一键启动.log` - 主程序日志
- `stock_data_fetch.log` - 股票数据获取日志
- `dividend_fetch.log` - 分红数据获取日志

## 故障排除
1. 如果遇到网络错误，请检查网络连接
2. 如果遇到API限制，脚本会自动重试
3. 如果某个股票数据获取失败，会记录到日志中并继续处理下一个

## 更新日志
- 2024-01-XX: 初始版本，支持CSV格式输出
- 数据按日期升序排列
- 添加一键启动功能
