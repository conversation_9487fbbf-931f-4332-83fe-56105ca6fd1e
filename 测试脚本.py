#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证所有脚本的语法正确性
"""

import os
import sys
import ast
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_syntax(file_path):
    """检查Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 尝试解析AST
        ast.parse(source_code)
        logger.info(f"✓ {file_path} - 语法正确")
        return True
        
    except SyntaxError as e:
        logger.error(f"✗ {file_path} - 语法错误: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ {file_path} - 检查失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始检查Python脚本语法...")
    
    # 要检查的脚本文件
    scripts = [
        '全量股票日线.py',
        '指数全量.py', 
        '财务数据分开表格全量更新.py',
        '分红数据分开表格全量更新.py',
        '一键启动.py'
    ]
    
    success_count = 0
    
    for script in scripts:
        if os.path.exists(script):
            if check_python_syntax(script):
                success_count += 1
        else:
            logger.warning(f"⚠️  文件不存在: {script}")
    
    logger.info(f"\n检查完成: {success_count}/{len(scripts)} 个脚本语法正确")
    
    if success_count == len(scripts):
        logger.info("🎉 所有脚本语法检查通过！")
        return True
    else:
        logger.warning("⚠️  部分脚本存在语法问题")
        return False

if __name__ == "__main__":
    main()
    input("按回车键退出...")
