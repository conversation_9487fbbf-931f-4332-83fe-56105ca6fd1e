import tushare as ts
import pandas as pd
import time
import re
import os

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
CSV_OUTPUT_DIR = "K:\\数据库存储地"

# Initialize
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# Create output directory if it doesn't exist
os.makedirs(CSV_OUTPUT_DIR, exist_ok=True)

def get_all_stocks():
    """Get all stock codes"""
    print("Fetching all stock codes...")
    stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
    print(f"Found {len(stock_basic)} stocks")
    return stock_basic['ts_code'].tolist()

def clean_filename(ts_code):
    """Convert stock code to valid filename"""
    # Remove dots and convert to lowercase
    return re.sub(r'[^\w]', '_', ts_code.lower())

def fetch_all_financial_data():
    """Fetch comprehensive financial data for all stocks - save as CSV files"""
    
    # Get all stock codes
    stock_codes = get_all_stocks()
    
    for i, stock_code in enumerate(stock_codes):
        print(f"Processing {stock_code} ({i+1}/{len(stock_codes)})")
        
        try:
            file_suffix = clean_filename(stock_code)

            # Balance Sheet
            balance_df = pro.balancesheet(
                ts_code=stock_code,
                fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,total_share,cap_rese,undistr_profit,surplus_rese,special_rese,money_cap,trad_asset,notes_receiv,accounts_receiv,oth_receiv,prepayment,div_receiv,int_receiv,inventories,amor_exp,nca_within_1y,sett_rsrv,loanto_oth_bank_fi,premium_receiv,reinsur_receiv,reinsur_res_receiv,pur_resale_fa,oth_cur_assets,total_cur_assets,fa_avail_for_sale,htm_invest,lt_eqt_invest,invest_real_estate,time_deposits,oth_assets,lt_rec,fix_assets,cip,const_materials,fixed_assets_disp,produc_bio_assets,oil_and_gas_assets,intan_assets,r_and_d,goodwill,lt_amor_exp,defer_tax_assets,decr_in_disbur,oth_nca,total_nca,cash_reser_cb,depos_in_oth_bfi,prec_metals,deriv_assets,rr_reins_une_prem,rr_reins_outstd_cla,rr_reins_lins_liab,rr_reins_lthins_liab,refund_depos,ph_pledge_loans,refund_cap_depos,indep_acct_assets,client_depos,client_prov,transac_seat_fee,invest_as_receiv,total_assets,st_borr,borr_frm_cb,depos_frm_oth_bfi,agent_trad_secu,agent_underw_secu,payroll_payable,taxes_payable,int_payable,div_payable,oth_payable,acc_exp,deferred_inc,st_bonds_payable,payable_to_reinsurer,rsrv_insur_cont,acting_trading_sec,acting_uw_sec,non_cur_liab_due_1y,oth_cur_liab,total_cur_liab,bond_payable,lt_payable,specific_payables,estimated_liab,defer_tax_liab,defer_inc_non_cur_liab,oth_ncl,total_ncl,depos_oth_bfi,deriv_liab,depos,agency_bus_liab,oth_liab,prem_receiv_adva,depos_received,ph_invest,reser_une_prem,reser_outstd_claims,reser_lins_liab,reser_lthins_liab,indept_acc_liab,pledge_borr,indem_payable,policy_div_payable,total_liab,treasury_share,ordin_risk_reser,forex_differ,invest_loss_unconf,minority_int,total_hldr_eqy_exc_min_int,total_hldr_eqy_inc_min_int,total_liab_hldr_eqy,lt_payroll_payable,oth_comp_income,oth_eqt_tools,oth_eqt_tools_p_shr,lending_funds,acc_receivable,st_fin_payable,payables,hfs_assets,hfs_sales'
            )
            if not balance_df.empty:
                # Sort by date (ascending order - oldest to newest)
                balance_df = balance_df.sort_values('end_date', ascending=True)
                csv_filename = f'balance_sheet_{file_suffix}.csv'
                csv_filepath = os.path.join(CSV_OUTPUT_DIR, csv_filename)
                balance_df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                print(f"  Balance Sheet: {len(balance_df)} records")
            
            # Income Statement
            income_df = pro.income(
                ts_code=stock_code,
                fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,basic_eps,diluted_eps,total_revenue,revenue,int_income,prem_earned,comm_income,n_commis_income,n_oth_income,n_oth_b_income,prem_income,out_prem,une_prem_reser,reins_income,n_sec_tb_income,n_sec_uw_income,n_asset_mg_income,oth_b_income,fv_value_chg_gain,invest_income,ass_invest_income,forex_gain,total_cogs,oper_cost,int_exp,comm_exp,biz_tax_surchg,sell_exp,admin_exp,fin_exp,assets_impair_loss,prem_refund,compens_payout,reser_insur_liab,div_payt,reins_exp,oper_exp,compens_payout_refu,insur_reser_refu,reins_cost_refund,other_bus_cost,operate_profit,non_oper_income,non_oper_exp,nca_disploss,total_profit,income_tax,n_income,n_income_attr_p,minority_gain,oth_compr_income,t_compr_income,compr_inc_attr_p,compr_inc_attr_m_s,ebit,ebitda,insurance_exp,undist_profit,distable_profit,rd_exp,fin_exp_int_exp,fin_exp_int_inc,transfer_surplus_rese,transfer_housing_imprest,transfer_oth,adj_lossgain,withdra_legal_surplus,withdra_legal_publi,withdra_biz_devfund,withdra_rese_fund,withdra_oth_ersu,workers_welfare,distr_profit_shrhder,prfshare_payable_dvd,comshare_payable_dvd,capit_comstock_div,continued_net_profit,end_net_profit'
            )
            if not income_df.empty:
                # Sort by date (ascending order - oldest to newest)
                income_df = income_df.sort_values('end_date', ascending=True)
                csv_filename = f'income_statement_{file_suffix}.csv'
                csv_filepath = os.path.join(CSV_OUTPUT_DIR, csv_filename)
                income_df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                print(f"  Income Statement: {len(income_df)} records")
            
            # Cash Flow Statement
            cashflow_df = pro.cashflow(
                ts_code=stock_code,
                fields='ts_code,ann_date,f_ann_date,end_date,report_type,comp_type,net_profit,finan_exp,c_fr_sale_sg,recp_tax_rends,n_depos_incr_fi,n_incr_loans_cb,n_inc_borr_oth_fi,prem_fr_orig_contr,n_incr_insured_dep,n_reinsur_prem,n_incr_disp_tfa,ifc_cash_incr,n_incr_disp_faas,n_incr_loans_oth_bank,n_cap_incr_repur,c_fr_oth_operate_a,c_inf_fr_operate_a,c_paid_goods_s,c_paid_to_for_empl,c_paid_for_taxes,n_incr_clt_loan_adv,n_incr_dep_cbob,c_pay_claims_orig_inco,pay_handling_chrg,pay_comm_insur_plcy,oth_cash_pay_oper_act,st_cash_out_act,n_cashflow_act,oth_recp_ral_inv_act,c_disp_withdrwl_invest,c_recp_return_invest,n_recp_disp_fiolta,n_recp_disp_sobu,stot_inflows_inv_act,c_pay_acq_const_fiolta,c_paid_invest,n_disp_subs_oth_biz,oth_pay_ral_inv_act,n_incr_pledge_loan,stot_out_inv_act,n_cashflow_inv_act,c_recp_borrow,proc_issue_bonds,oth_cash_recp_ral_fnc_act,stot_cash_in_fnc_act,free_cashflow,c_prepay_amt_borr,c_pay_dist_dpcp_int_exp,incl_dvd_profit_paid_sc_ms,oth_cashpay_ral_fnc_act,stot_cashout_fnc_act,n_cash_flows_fnc_act,eff_fx_flu_cash,n_incr_cash_cash_equ,c_cash_equ_beg_period,c_cash_equ_end_period,c_recp_cap_contrib,incl_cash_rec_saims,uncon_invest_loss,prov_depr_assets,depr_fa_coga_dpba,amort_intang_assets,lt_amort_deferred_exp,decr_deferred_exp,incr_acc_exp,loss_disp_fiolta,loss_scr_fa,loss_fv_chg,invest_loss,decr_def_inc_tax_assets,incr_def_inc_tax_liab,decr_inventories,decr_oper_payable,incr_oper_payable,others,im_net_cashflow_oper_act,conv_debt_into_cap,conv_copbonds_due_within_1y,fa_fnc_leases,end_bal_cash,beg_bal_cash,end_bal_cash_equ,beg_bal_cash_equ,im_n_incr_cash_equ'
            )
            if not cashflow_df.empty:
                # Sort by date (ascending order - oldest to newest)
                cashflow_df = cashflow_df.sort_values('end_date', ascending=True)
                csv_filename = f'cash_flow_{file_suffix}.csv'
                csv_filepath = os.path.join(CSV_OUTPUT_DIR, csv_filename)
                cashflow_df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                print(f"  Cash Flow: {len(cashflow_df)} records")
                
            # Rate limiting - avoid hitting API limits
            time.sleep(0.1)
            
        except Exception as e:
            print(f"Error processing {stock_code}: {e}")
            continue
    
    print("All stocks processed successfully!")

if __name__ == "__main__":
    fetch_all_financial_data()


