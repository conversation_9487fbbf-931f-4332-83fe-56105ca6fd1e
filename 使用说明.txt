股票数据获取工具 - 使用说明
=====================================

📁 数据存储位置：K:\数据库存储地

🚀 快速开始：
1. 双击运行 "启动数据获取.bat" 
2. 按照提示操作即可

📋 功能说明：
✅ 全量股票日线数据 - 获取所有A股的日线行情数据
✅ 指数全量数据 - 获取主要指数的历史数据  
✅ 财务数据 - 获取资产负债表、利润表、现金流量表
✅ 分红数据 - 获取分红派息信息

📊 输出文件夹结构：
K:\数据库存储地\
├── 股票日线数据/
│   └── stock_daily_{股票代码}.csv
├── 指数数据/
│   └── index_{指数代码}.csv
├── 财务数据/
│   ├── 资产负债表/
│   │   └── balance_sheet_{股票代码}.csv
│   ├── 利润表/
│   │   └── income_statement_{股票代码}.csv
│   └── 现金流量表/
│       └── cash_flow_{股票代码}.csv
└── 分红数据/
    └── dividend_{股票代码}.csv

⚡ 特性：
• 数据按日期升序排列（从旧到新）
• 支持中文编码（UTF-8-BOM）
• 自动重试机制
• 详细日志记录

⚠️ 注意事项：
• 需要稳定的网络连接
• 数据获取需要较长时间，请耐心等待
• 如果某个脚本失败，可以选择继续执行下一个

📝 日志文件：
• 一键启动.log - 主程序日志
• stock_data_fetch.log - 股票数据日志
• dividend_fetch.log - 分红数据日志

🔧 单独运行脚本：
如需单独运行某个脚本，可以直接运行对应的.py文件：
• 全量股票日线.py
• 指数全量.py
• 财务数据分开表格全量更新.py
• 分红数据分开表格全量更新.py

💡 提示：
建议在非交易时间运行，避免网络拥堵
首次运行可能需要几个小时，请确保电脑不会休眠

如有问题，请查看对应的日志文件获取详细错误信息。
