@echo off
chcp 65001 >nul
title 股票数据一键获取工具

echo ========================================
echo           股票数据一键获取工具
echo ========================================
echo.
echo 本工具将自动运行以下数据获取脚本：
echo 1. 全量股票日线数据
echo 2. 指数全量数据  
echo 3. 财务数据（资产负债表、利润表、现金流量表）
echo 4. 分红数据
echo.
echo 数据将保存为CSV格式到: K:\数据库存储地
echo.
echo 文件夹结构:
echo ├── 股票日线数据/
echo ├── 指数数据/
echo ├── 财务数据/
echo │   ├── 资产负债表/
echo │   ├── 利润表/
echo │   └── 现金流量表/
echo └── 分红数据/
echo.
echo ========================================

python "一键启动.py"

echo.
echo ========================================
echo 程序执行完毕
echo ========================================
pause
